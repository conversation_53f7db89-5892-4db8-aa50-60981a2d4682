<template>
  <div class="graph-content">
    <div class="left">
      <knowledgeTree
        ref="knowledgeTreeRef"
        :selected="chapterId"
        :iswen="iswen"
        :isStatus="true"
        :showTaskBadge="true"
        :options="options.value"
        :selectName="selectName"
        :is-history-task="query.contentType === 'historyTask'"
        @setChapterId="setChapterId"
      />
      <div class="title-coustom">
        选择章节
        <span></span>
      </div>
    </div>
    <div class="right" :class="pageStatus ? '' : 'test-bgimg'">
      <div class="content-head">
        <div class="head-body">
          <img src="@/assets/img/percision/training/textbook.png" alt="" class="textbook" />
          <div class="head-title">
            当前教材： {{ bookVersionName }}
          </div>
          <div class="head-switch" v-if="!query.contentType">切换教材</div>
        </div>

        <img @click="onMark" src="@/assets/img/percision/training/superficiality.png" alt="" class="superficiality" />
        <div class="catalogue">
          <span>{{chapterName}}</span>
          <img src="@/assets/img/percision/training/dsj.png" alt="">
        </div>      
      </div>
      <!-- <div class="book-vision">
        <div style="display: flex;">
          <div class="book-vision-text" v-if="!query.contentType">
            <span>当前教材：</span>
            {{ bookVersionName }}
          </div>
          <div class="book-vision-text" v-else>
            <span>当前教材：</span>
            {{ query.editionName }} {{query.gradeName}}{{ query.termName }}
          </div>
        </div>
        <div v-if="!pageStatus" class="my-paper-btn" @click="toMyPaper">
          <img src="@/assets/img/percision/write.png" alt=""> 我的试卷
        </div>
      </div> -->
      <!-- <div class="gray-box" >
        <div>{{ chapterName }}</div>
        <ArrowDown class="icon-sty"></ArrowDown>
      </div> -->

      <div v-if="testList.length > 0">
        <div v-if="pageStatus">
          <div class="graph-box">
            <graph :icons="testList" :sourceId="chapterId" v-loading="loading" :step="step" :isSecond="true" @startLearn="startLearn" styles="height: 33.125rem;width: 52.1875rem" :icon-size="50" />
          </div>
          <div class="start-learn">
            <div class="start-learn-btn" @click="startLearn2">
              <div v-if="step == 1"> 弱项检测 </div>
              <div v-if="step == 2">
                <img src="@/assets/img/percision/play.png" alt="">
                针对学习
              </div>
              <div v-if="step == 4"> 阶段测试 </div>
              <div v-if="step == 5"> 错题消化 </div>
            </div>
            <p class="start-learn-tips">每个知识点3道题，测评你本单元的掌握度</p>
            <el-divider border-style="dashed" />
            <div class="lefend-box">
              <div class="lefend-box-tip">图例</div>
              <div class="lefend-box-item">
                掌握<span class="green"></span>
              </div>
              <div class="lefend-box-item">
                一般<span class="origin"></span>
              </div>
              <div class="lefend-box-item">
                未掌握<span class="red"></span>
              </div>
              <div class="lefend-box-item">
                未测<span class="grey"></span>
              </div>
            </div>
          </div>
        </div>
        <div v-else v-loading="loading">
          <div class="test-box">
            <div class="test-wrap" v-for="item in testList">
              <div class="test-box-item">
                <div class="test-box-item-img">
                  <span class="red-text" v-if="item.score&&item.score!='0'">{{item.score}}分</span>
                </div>
                <div class="test-box-item-info">
                  <div class="test-box-item-info-title">
                    {{item.title}}
                  </div>
                  <div class="test-box-item-info-data">
                    <div>更新时间：{{item.reportDate	}}</div>
                    <div>浏览：{{ item.viewCount }}</div>
                  </div>
                </div>
                <div class="test-box-item-btn">
                  <div class="test-box-item-btn-it btn" @click="handleDownload(item)">
                    <img src="@/assets/img/percision/download.png" alt=""> 下载
                  </div>
                  <div class="test-box-item-btn-it blue-text" @click="testDetail(item)">
                    查看详情>
                  </div>
                </div>
              </div>
              <div class="hui-line"></div>
            </div>
          </div>
          <div class="pagination-box">
            <Pagination
                :total="pageData.total"
                :current="pageData.current"
                @currentSizeChange="currentSizeChange"
                @pageClick="pageClick"/>
          </div>
        </div>
      </div>
      <div class="empty" v-else v-loading="loading">
        <img src="@/assets/img/percision/empty.png" alt="del" />
        <p>空空如也</p>
      </div>
    </div>
    <div class="five-step-box" v-if="!isTest">>
      <fiveStep :sourceId="chapterId" :type="fiveType" :update="true" @sendStep="sendStep"></fiveStep>
    </div>
  </div>
  <!-- 下载试卷 -->
  <downloadTrestDialog v-if="dialogVisible" ref="downloadTrestDialogRef" :paper-detail="dowmloadData" />
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { useRoute } from 'vue-router'
import knowledgeTree from "@/views/components/knowledgeTree/index.vue"
import { getpointListApi, getChapterListApi } from "@/api/book"
import { getChapterReportListApi } from "@/api/report"
import { getImprovementPointList, getSonPointList, pointListList, getStudyPointApi } from "@/api/video"
import { ArrowDown } from '@element-plus/icons-vue'
import fiveStep from "@/views/components/fiveStep/index.vue"
import graph from "@/views/components/graph/index.vue"
import { computed, nextTick, onMounted, reactive, ref, onUnmounted } from 'vue'
import { useUserStore } from "@/store/modules/user"
import { dataEncrypt } from "@/utils/secret"
import { storeToRefs } from 'pinia'
import downloadTrestDialog from "@/components/TruePaperDownload/index.vue"
const userStore = useUserStore()

const { subjectObj, learnNow } = storeToRefs(userStore)

const router = useRouter()
const route = useRoute()
const dataType =Number(route.query.dataType) || 1

const type = route.query.type
const query = route.query as any

// const fiveType:any = computed(() => {
//   return type=='synchronous'?1:route.query.fiveType||2
// })
const fiveType:any = computed(() => {
  return type==='synchronous'?1:type==='improvement'||type==='examPrep'?2:route.query.fiveType||1
})
const isTest=computed(() => {
  return userStore.chapterObj.chapterName.includes("单元测试")
})

// 根据模式获取存储的选中信息（包含id和name）
const getStoredSelectedInfo = () => {
  const storageKey = `selectedChapterInfo_${type || 'synchronous'}`
  const stored = localStorage.getItem(storageKey)
  try {
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.error('解析存储的选中信息失败:', error)
    return null
  }
}

// 根据模式存储选中的信息（包含id和name）
const setStoredSelectedInfo = (id: string, name: string) => {
  const storageKey = `selectedChapterInfo_${type || 'synchronous'}`
  const selectedInfo = { id, name }
  localStorage.setItem(storageKey, JSON.stringify(selectedInfo))
}

// 清除存储的选中信息
const clearStoredSelectedInfo = () => {
  const storageKey = `selectedChapterInfo_${type || 'synchronous'}`
  localStorage.removeItem(storageKey)
}

// 查看所有模式的存储状态（调试用）
const getAllStoredInfo = () => {
  const modes = ['synchronous', 'improvement', 'examPrep']
  const allStoredInfo = {}
  modes.forEach(mode => {
    const storageKey = `selectedChapterInfo_${mode}`
    const stored = localStorage.getItem(storageKey)
    try {
      allStoredInfo[mode] = stored ? JSON.parse(stored) : null
    } catch (error) {
      allStoredInfo[mode] = null
    }
  })
  return allStoredInfo
}

// 初始化章节名称，优先使用存储的名称
const initChapterName = () => {
  const storedInfo = getStoredSelectedInfo()
  if (storedInfo && storedInfo.name) {
    return storedInfo.name
  }
  return userStore.chapterObj.chapterName
}

const chapterName = ref(initChapterName())
const loading = ref(false)
const step = ref(1)
const pageStatus = ref(true)

// 初始化chapterId，优先使用存储的ID
const initChapterId = () => {
  const storedInfo = getStoredSelectedInfo()
  if (storedInfo) {
    return storedInfo.id
  }
  // 如果没有存储的ID，使用默认值并存储
  const defaultId = userStore.chapterObj.chapterId?.replace('章节', '')?.replace('单元测试', '')
  if (defaultId) {
    setStoredSelectedInfo(defaultId, userStore.chapterObj.chapterName)
  }
  return defaultId
}

const chapterId = ref(initChapterId())
const selectName= computed(() => {
  return userStore.chapterObj.chapterName
})
const options = userStore.chapterList || []
const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})
const testList = ref([] as any[])
const iswen=ref( route.query.type==='improvement'|| route.query.type === 'examPrep'?true:false)//左侧菜单label
const bookVersionName = computed(() => {
  return subjectObj.value.editionName + learnNow.value.gradeName + (subjectObj.value.termName?subjectObj.value.termName:"")
})

const knowledgeTreeRef = ref() // 添加ref用于访问知识树组件

onMounted(async () => {
  // console.log(query.contentType,"query.contentType")
  pageStatus.value = !userStore.chapterObj.chapterName.includes("单元测试")
  getAllStoredInfo() // 显示所有模式的存储状态

  // 如果是历史任务，直接加载章节列表
  if(query.contentType === "historyTask" && query.chapterId) {
    await getChapterList() // 这会触发handleDefaultSelection，处理历史任务的选中逻辑
  } else {
    // 正常流程，先获取当前知识节点
    await getStudyPoint() // 这会调用getChapterList并处理选中逻辑
  }

  // 根据页面状态加载对应数据
  // console.log(pageStatus.value,"pageStatus.valuepageStatus.valuepageStatus.value")
  if(pageStatus.value) {
    // 如果不是单元测试，加载知识点列表
    // 2次调用可以隐藏
    getpointList()
  } else {
    // 如果是单元测试，加载试卷数据
    // 2次调用可以隐藏
    getChapterReportList()
  }

  window.customGoBack = customGoBack
})

const customGoBack = () => {
    // router.go(-1)
    router.push('/ai_percision/knowledge_graph')
}

onUnmounted(() => {
  // 清除自定义返回方法
  if (window.customGoBack) {
    delete window.customGoBack
  }
})

// 获取当前知识节点
const getStudyPoint = async()=>{
  // 如果是历史任务，不需要从存储中获取选中信息
  if(query.contentType === "historyTask" && query.chapterId) {
    // 历史任务的选中逻辑将在handleDefaultSelection中处理
    return
  }

  // 先尝试获取存储的选中信息
  const storedInfo = getStoredSelectedInfo()
  if (storedInfo&&storedInfo.id) {
    chapterId.value = storedInfo.id
    chapterName.value = storedInfo.name
  }

  // 获取章节列表，完成后处理默认选中逻辑
  await getChapterList()

  // 如果没有存储的ID，则获取默认的知识节点
  // const res:any = await getStudyPointApi({
  //     subject:subjectObj.value.id,
  //     isNoPass:false
  //   })
  // if(res.code == 200) {
  //   chapterId.value = res.data.id
  //   console.log("使用默认知识节点ID:",chapterId.value)
  //   // 存储默认获取的ID
  //   setStoredSelectedInfo(chapterId.value)
  // }
}
const onMark = () => {
  router.push({
    path: '/ai_percision/knowledge_hotspot',
  })
}

//获取章节知识点树   左侧章节目录
const getChapterList = async() => {
  loading.value = true
  try {
      const type = route.query.type
      let res: any
      if (type === 'improvement') {
        // 如果type为improvement，调用getImprovementPointList
        res = await getImprovementPointList({
          subject:subjectObj.value.id,
          hierarchy:route.query.dataType
        })

      }else if (type === 'examPrep'){
        res = await pointListList({
          subject:subjectObj.value.id,
          hierarchy:route.query.dataType,
        })
      } else {
        // 默认调用getChapterListApi
        res =  await getChapterListApi({
          bookId: query?.bookId || subjectObj.value.bookId,
          chapterIds: query?.chapterId
        })
      }

    loading.value = false
    if(res.code == 200) {
      options.value = res.data || []
      userStore.setChapterList(options.value)

      // 处理选中逻辑
      handleDefaultSelection()
    }
  } catch (error) {
    loading.value = false
  }
}

// 处理默认选中逻辑
const handleDefaultSelection = () => {
  // 处理历史任务的情况
  if(query.contentType === "historyTask" && query.chapterId) {
    // 在章节树中查找匹配的节点
    const matchedNode = findNodeByChapterId(options.value, query.chapterId)
    if(matchedNode) {
      const nodeId = iswen.value ? matchedNode.id : matchedNode.chapterId
      const nodeName = iswen.value ? matchedNode.name : matchedNode.chapterName

      chapterId.value = nodeId
      chapterName.value = nodeName

      // 存储选中的信息
      setStoredSelectedInfo(nodeId, nodeName)

      // 使用nextTick确保DOM更新后再调用
      nextTick(() => {
        setChapterId(matchedNode, nodeName)
        // 滚动到选中节点
        scrollToSelectedNode()
      })
      return
    }
  }

  const storedInfo = getStoredSelectedInfo()

  if (storedInfo && storedInfo.id && options.value.length > 0) {
    // 有存储信息，验证这个ID在当前options中是否存在
    const nodeExists = findNodeById(options.value, storedInfo.id)
    if (nodeExists) {
      // 构造适合setChapterId的data对象
      const dataForSetChapterId = iswen.value ?
        { id: storedInfo.id, name: storedInfo.name, children: [] } :
        { chapterId: storedInfo.id, chapterName: storedInfo.name, children: [] }

      // 先设置值，再调用setChapterId
      chapterId.value = storedInfo.id
      chapterName.value = storedInfo.name

      // 使用nextTick确保DOM更新后再调用
      nextTick(() => {
        setChapterId(dataForSetChapterId, storedInfo.name)
        // 滚动到选中节点
        scrollToSelectedNode()
      })
    } else {
      // console.log("存储的ID在当前options中不存在，使用默认选择")
      selectFirstOption()
    }
  } else if (options.value.length > 0) {
    // 没有存储信息，使用options第一条数据作为默认值
    selectFirstOption()
  }
}

// 选择第一个可用选项
const selectFirstOption = () => {
  const firstOption = findFirstLeafNode(options.value)
  if (firstOption) {
    // console.log("使用默认选中（options第一条数据）:", firstOption)
    const defaultId = iswen.value ? firstOption.id : firstOption.chapterId
    const defaultName = iswen.value ? firstOption.name : firstOption.chapterName

    chapterId.value = defaultId
    chapterName.value = defaultName

    // 使用nextTick确保DOM更新后再调用
    nextTick(() => {
      setChapterId(firstOption, defaultName)
    })
  }
}

// 查找节点是否存在
const findNodeById = (nodes: any[], targetId: string): any => {
  for (const node of nodes) {
    const nodeId = iswen.value ? node.id : node.chapterId
    if (nodeId === targetId) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, targetId)
      if (found) return found
    }
  }
  return null
}

// 查找第一个叶子节点
const findFirstLeafNode = (nodes: any[]): any => {
  for (const node of nodes) {
    if (node.children && node.children.length > 0) {
      const leafNode = findFirstLeafNode(node.children)
      if (leafNode) return leafNode
    } else {
      return node
    }
  }
  return null
}

// 根据chapterId查找节点
// 根据chapterId查找节点（只匹配最后一级/叶子节点）
const findNodeByChapterId = (nodes: any[], targetChapterId: string): any => {
  const matchedNodes: any[] = []

  // 递归查找所有匹配的叶子节点
  const findAllMatches = (nodeList: any[]) => {
    for (const node of nodeList) {
      // 检查是否为叶子节点（没有子节点或子节点为空）
      const isLeafNode = !node.children || node.children.length === 0

      if (isLeafNode) {
        // 只在叶子节点中匹配 chapterId
        const nodeId = iswen.value ? node.id : node.chapterId
        if (nodeId === targetChapterId) {
          matchedNodes.push(node)
        }
      } else {
        // 如果不是叶子节点，继续递归查找子节点
        findAllMatches(node.children)
      }
    }
  }

  // 查找所有匹配的节点
  findAllMatches(nodes)

  // 如果没有匹配的节点，返回 null
  if (matchedNodes.length === 0) {
    return null
  }

  // 如果只有一个匹配的节点，直接返回
  if (matchedNodes.length === 1) {
    return matchedNodes[0]
  }

  // 如果有多个匹配的节点，且URL中有name参数，进行精确匹配
  if (matchedNodes.length > 1 && query.name) {
    const urlName = query.name as string
    const urlHasUnitTest = urlName.includes('单元测试')

    // 查找chapterName与URL的name都包含"单元测试"的节点
    const exactMatch = matchedNodes.find(node => {
      const nodeName = iswen.value ? node.name : node.chapterName
      const nodeHasUnitTest = nodeName && nodeName.includes('单元测试')

      // 如果URL的name和节点的chapterName都包含"单元测试"，则匹配
      if (urlHasUnitTest && nodeHasUnitTest) {
        return true
      }

      // 如果URL的name和节点的chapterName都不包含"单元测试"，也可以匹配
      if (!urlHasUnitTest && !nodeHasUnitTest) {
        return nodeName === urlName
      }

      return false
    })

    if (exactMatch) {
      return exactMatch
    }
  }

  // 如果没有URL name参数或没有精确匹配，优先选择包含"单元测试"的节点
  const unitTestNode = matchedNodes.find(node => {
    const nodeName = iswen.value ? node.name : node.chapterName
    return nodeName && nodeName.includes('单元测试')
  })

  // 返回包含"单元测试"的节点，或第一个匹配的节点
  return unitTestNode || matchedNodes[0]
}

//获取试卷列表
const getChapterReportList = async() => {
  loading.value = true
  try {
    const res: any = await getChapterReportListApi({
      chapterId: chapterId.value,
      bookId: subjectObj.value.bookId,
      isSmall: 1,
      type: 1,
      size: pageData.size,
      current: pageData.current
    })
    if(res.code == 200) {
      testList.value = res.data.records || []
      pageData.total = Number(res.data.total)
    }
    loading.value = false
  } catch (error) {
    testList.value = []
    console.log(error)
    loading.value = false
  }
}
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  getChapterReportList()
}
const pageClick = (val: number) => {
  pageData.current = val
  getChapterReportList()
}
// 获取学习步骤
const sendStep = ( data: number) => {
  step.value = data
}

// const getpointList = async() => {
//   loading.value = true
//   try {
//     const res: any = await getpointListApi({
//       chapterId: chapterId.value,
//     })
//     if(res.code == 200) {
//       testList.value = res.data || []
//     }
//     loading.value = false
//   } catch (error) {
//     loading.value = false
//   }
// }

// 获取章节下知识点列表
const getpointList = async() => {
  loading.value = true
  try {
   // 获取URL中的type参数
   const type = route.query.type
      let res: any
      if (type === 'improvement'||type === 'examPrep') {
        // 如果type为improvement，调用getImprovementPointList
        res = await getSonPointList({
          pointId: chapterId.value,
        })

      } else {
        if(query.contentType == "historyTask"){
          // 如果是历史任务，使用query.chapterId
          res = await getpointListApi({
            chapterId: chapterId.value, // 使用当前选中的chapterId，而不是直接使用query.chapterId
          })
        }else{
          // 默认调用getpointListApi
          res = await getpointListApi({
              chapterId: chapterId.value,
          })
        }

      }
    if(res.code == 200) {
      testList.value = res.data || []
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}
// 老版本
// const setChapterId = (data: any, name: string) => {
//   pageStatus.value = data.chapterName != "单元测试"
//   chapterId.value = data.chapterId
//   chapterName.value = name
//   nextTick(() => {
//     userStore.setChapterId(chapterId.value, name)
//   })
//   if(data.chapterName == "单元测试") {
//     getChapterReportList()
//   } else {
//     getpointList()
//   }
// }

const setChapterId = (data: any, name: string) => {
  pageStatus.value = route.query.type==='improvement'||route.query.type === 'examPrep'?data.name:data.chapterName != "单元测试"
  chapterId.value =route.query.type==='improvement'||route.query.type === 'examPrep'?data.id: data.chapterId
  chapterName.value = name
  let typeName='同步模式'
  switch (type) {
      case 'synchronous':
      typeName = '同步模式'
          break
      case 'improvement':
      typeName = '提高模式'
          break
      case 'examPrep':
      typeName = '备考模式'
          break
  }

  // 只有在非historyTask模式下才存储选中信息
  if(query.contentType !== "historyTask") {
    // 存储当前选中的信息（id和name）
    setStoredSelectedInfo(chapterId.value, name)
    // console.log('已存储选中信息:', { id: chapterId.value, name: name })
  }

  nextTick(() => {
   userStore.setChapterId(chapterId.value, name,typeName)
   // 滚动到选中节点
   scrollToSelectedNode()
  })

  // 根据页面状态决定加载什么数据
  if(data.chapterName == "单元测试") {
    // console.log("加载试卷数据")
    getChapterReportList()
  } else {
    // console.log("加载知识点数据")
    getpointList()
  }
}

// 根据步骤跳转至不同页面-单个
const startLearn = (e: any) => {
  let url = "/ai_percision/knowledge_graph_detail/weekness_check"
  if(step.value == 1) {
    url = "/ai_percision/knowledge_graph_detail/weekness_check"
  } else if(step.value == 2) {
    url = "/ai_percision/knowledge_graph_detail/regarding_learning"
  } else if(step.value == 4) {
    url = "/ai_percision/knowledge_graph_detail/periodical_check"
  } else if(step.value == 5) {
    url = "/ai_percision/knowledge_graph_detail/worning_learning"
  }
  const pointId = [e.id]
  router.push({
    path: url,
    query: {
      data: dataEncrypt({
        sourceId: chapterId.value,
        chapterId: chapterId.value,
        pointId,
        step: step.value,
        subject: subjectObj.value.id,
        type:type,
        dataType:dataType,
        contentType:query.contentType
      }),
      type:type,
      contentType:query.contentType
    }
  })
}

// 根据步骤跳转至不同页面-非掌握
const startLearn2 = () => {
  let url = "/ai_percision/knowledge_graph_detail/weekness_check"
  if(step.value == 1) {
    url = "/ai_percision/knowledge_graph_detail/weekness_check"
  } else if(step.value == 2) {
    url = "/ai_percision/knowledge_graph_detail/regarding_learning"
  } else if(step.value == 4) {
    url = "/ai_percision/knowledge_graph_detail/periodical_check"
  } else if(step.value == 5) {
    url = "/ai_percision/knowledge_graph_detail/worning_learning"
  }
  const pointId:any = []
  let list=testList.value
  for (let i of list) {
    if (i.status != 1) {
      pointId.push(i.id)
    }
  }
  router.push({
    path: url,
    query: {
      data: dataEncrypt({
        sourceId: chapterId.value,
        chapterId: chapterId.value,
        pointId,
        step: step.value,
        subject: subjectObj.value.id,
        type: type,
        dataType:dataType,
        contentType:query.contentType
      }),
      type:type,
      contentType:query.contentType
    }
  })
}

const testDetail = (data: any) => {
  router.push({
    path: '/ai_percision/knowledge_graph_detail_unit/paper_detailU',
    query: {
      data: dataEncrypt({
        reportId: data.id,
        pageSource: '3'
      }),
    }
  })
}
const toMyPaper = () => {
  router.push({
    path: '/ai_percision/knowledge_graph_detail_unit/my_paperU',
    query: {
      data: dataEncrypt({
        pageSource: '3'
      }),
    }
  })
}
//下载试卷
const downloadTrestDialogRef = ref()
const dowmloadData = reactive({
    id: '',
    title: ''
})
const dialogVisible = ref(false)
const handleDownload = ({ id, title }: any) => {
  Object.assign(dowmloadData, {
    id: id,
    title: title
  })
  dialogVisible.value = true
  nextTick(() => {
    downloadTrestDialogRef.value.dialogShow()
  })
}

// 滚动到选中的节点
const scrollToSelectedNode = () => {
  // 确保DOM已更新
  nextTick(() => {
    // 尝试获取知识树组件实例
    if (knowledgeTreeRef.value) {
      // 如果组件提供了scrollToSelected方法，则调用它
      if (typeof knowledgeTreeRef.value.scrollToSelected === 'function') {
        knowledgeTreeRef.value.scrollToSelected()
      } else {
        // 如果组件没有提供方法，则尝试通过DOM查找选中节点
        const selectedNode = document.querySelector('.el-tree-node.is-current')
        if (selectedNode) {
          // 使用scrollIntoView滚动到选中节点
          selectedNode.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }
    }
  })
}
</script>
<style scoped lang="scss">
.graph-content {
  display: flex;
  position: relative;
}
.five-step-box {
  position: absolute;
  right: -8.75rem;
  top: 11.25rem;
}
.left {
  position: relative;
  .title-coustom {
    width: 11.1875rem;
    height: 2.875rem;
    line-height: 2.875rem;
    text-align: center;
    border-bottom-right-radius: .625rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    position: absolute;
    left: -0.875rem;
    top: .625rem;
    span{
      display: inline-block;
      border-top: .4375rem solid #00886E;
      border-left: .4375rem solid #F5F5F5;
      border-bottom: .4375rem solid #F5F5F5;
      border-right: .4375rem solid #00886E;
      position: absolute;
      bottom: -0.875rem;
      left: 0;

    }
  }
}
.right {
  margin-left: .625rem;
  width: 60.0625rem;
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  border-radius: 1.25rem ;
  // background-image: url(@/assets/img/percision/knowledge_graph2.png);
  background: #ffffff;
  background-size: cover;
  background-repeat: no-repeat;
    .content-head{
    position: relative;
    background: url('@/assets/img/percision/training/head-bg.png')center center no-repeat;
    background-size: cover;
    height: 121px;
    padding: 0 20px;
    .head-body{
      position: relative;
      padding: 20px 0 0 0px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .textbook{
        width: 24px;
        height: 30px;
        margin-right: 6px;
      }
      .head-title{
        margin-right: 10px;
      }
      .head-switch{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 92px;
        height: 27px;
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        background: #00957f;
        border-radius: 14px;
        cursor: pointer;
      }
    }
    .catalogue{
      background: #fff;
      border-radius: 22px;
      margin-top: 20px;
      line-height: 33px;
      padding-left: 16px;
      display: flex;
      span{
        color: rgba(102, 102, 102, 1);
        font-size: 16px;
      }
      img{
        width: 14px;
        height: 9px;
        margin: 12px 12px 12px auto;
      }
    }
    .superficiality{
      position: absolute;
      right: 0;
      top:0;
      width: 120px;
      height: 39px;
      cursor: pointer;
    }
    .head-select{
      position: relative;
      margin: 22px 0 0 20px;
      width: 882px;
      height: 33px;
    }
    .head-select :deep(.el-select__wrapper) {
        border-radius: 15px;
    }
  }
}
.test-bgimg {
  background-image: url(@/assets/img/percision/unit_test2.png);
}
.book-vision {
  margin-left: 13.3125rem;
  margin-top: 1.375rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  img {
    width: 1.5rem;
    height: 1.5rem;
    margin-right: .5rem;
  }
  .my-paper-btn {
    width: 6.875rem;
    height: 2.25rem;
    border-radius: .25rem;
    border: .0625rem solid #ffffff;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5625rem;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    img {
      width: 1rem;
      height: 1rem;
      margin-right: .25rem;
    }
  }
  .book-vision-text {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 700;
  }
}
.gray-box {
  margin: 2.8125rem 2.8125rem 0 2.8125rem;
  cursor: no-drop;
  display: flex;
  justify-content: space-between;
  padding: 0 1rem;
  height: 2.0625rem;
  line-height: 2.0625rem;
  box-sizing: border-box;
  border-radius: 1.375rem;
  border: .0625rem solid #eaeaea;
  background: #f5f5f5;
  color: #666666;
  font-size: 1rem;
  font-weight: 400;
}
.graph-box {
  margin-left: 2.8125rem;
  // margin-top: 3.5rem;
}
.start-learn {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 0 1.875rem;
  &-tips {
    text-align: center;
    color: #2a2b2a;
    font-size: .75rem;
  }
  &-btn {
    width: 14.0625rem;
    height: 3.125rem;
    line-height: 3.125rem;
    color: #ffffff;
    background: url(@/assets/img/percision/learn-btn-bg.png);
    background-size: 100%;
    background-repeat: no-repeat;
    div {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    cursor: pointer;
    font-size: 1.125rem;
    img {
      width: 1.25rem;
      height: 1.25rem;
      margin-right: .3125rem;
    }
  }
  :deep(.el-divider){
    margin: 0 0 .5625rem 0;
  }
  .lefend-box {
    width: 52rem;
    height: 3.125rem;
    border-radius: .625rem;
    background: #e5f9f6;
    display: flex;
    &-tip {
      width: 3.5rem;
      height: 1.5625rem;
      line-height: 1.5625rem;
      font-size: .875rem;
      font-weight: 700;
      color: #ffffff;
      border-radius: .625rem 0 .625rem 0;
      background: linear-gradient(144.8deg, #ffce39 0%, #ff9524 100%);
      text-align: center;
    }
    &-item {
      height: 3.125rem;
      color: #666666;
      font-size: .875rem;
      margin-left: 3.8125rem;
      display: flex;
      align-items: center;
      span {
        display: inline-block;
        margin-left: .625rem;
        width: 1.5rem;
        height: .875rem;
        border-radius: .125rem;
      }
    }
  }
}
.green {
  background: #00c9a3;
}
.origin {
  background: #FFAB23;
}
.red {
  background: #E84B4B;
}
.grey {
  background: #DBD6D1;
}
.test-box {
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 11.875rem);
  width: 54.5rem;
  margin-left: 1.5625rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: .625rem;
  position: relative;
  .test-wrap{
      width: calc(100% - .5rem);
      .hui-line{
        width: calc(100% - 1.75rem);
        border-bottom: .0625rem dashed #EAEAEA;
        margin: 0 0 0 .875rem;
        float: left;
      }
    }
    &-item {
      width: 100%;
      height: 6.875rem;
      display: flex;
      padding: 1.25rem 1.25rem 1.25rem 1.25rem;
      box-sizing: border-box;
      &:hover {
        background: #effdfb;
      }
      &-img {
        width: 3.1875rem;
        height: 100%;
        font-size: .75rem;
        background-image: url(@/assets/img/percision/test-img.png);
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        span {
          display: inline-block;
          margin-top: 1.85rem;
          position: relative;
          left: .125rem;
        }
      }
    &-info {
      margin-left: 1rem;
      width: 40rem;
      margin-right: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      &-title {
        color: #2a2b2a;
        font-size: 1rem;
        font-weight: 400;
      }
      &-data {
        div {
          height: 1.75rem;
          border-radius: .875rem;
          background: #fef8e9;
          color: #ef9d19;
          display: inline-block;
          box-sizing: border-box;
          padding: .375rem .75rem;
          font-size: .75rem;
          margin-right: .625rem;
        }
      }
    }
    &-btn {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &-it {
        width: 5.25rem;
        height: 1.875rem;
        line-height: 1.875rem;
        border-radius: .25rem;
        font-size: .875rem;
        text-align: center;
        cursor: pointer;
        img {
          width: .875rem;
          height: .875rem;
        }
      }
    }
  }
  .learn-img {
    position: fixed;
    bottom: 1.875rem;
    left: 55%;
    width: 14.0625rem;
    height: 3.125rem;
  }
}
.btn {
  color: #ffffff;
  background: #00c9a3;
}
.grey-btn {
  background: #f5f5f5;
  color: #999999;
}
.red-text {
  color: #dd2a2a;
}
.blue-text {
  color: #009c7f;
}
.icon-sty {
  width: 1rem;
}
.empty {
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 10rem);
  width: 53.75rem;
  margin-left: 1.875rem;
  text-align: center;
  padding-top: 13.9375rem;
  box-sizing: border-box;
  img {
    width: 7.4375rem;
    height: 8rem;
  }
  p {
    text-align: center;
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
  }
}

</style>
