<template>
    <div class="flex-box">
        <div class="con-box">
            <el-table
                :data="tableData"
                style="width: 100%">
                <el-table-column type="index" label="序号" width="50" />
                <el-table-column prop="name" label="科目" width="180"></el-table-column>
                <el-table-column prop="date" label="闯关内容" width="220"></el-table-column>
                <el-table-column label="闯关时间" prop="address"></el-table-column>
                <el-table-column label="正确率" prop="address"></el-table-column>
                <el-table-column label="查看">
                    <template #default="scope">
                        <span @click="openDetail(scope.row)">查看详情 ></span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script setup lang="ts">
const tableData = [
    { name: '王小虎', date: '2021-01-01', address: '上海市普陀区金沙江路 1518 弄' },
]
const openDetail = (row) => {
    console.log(row)
}
</script>
<style lang="scss" scoped>
.flex-box {
    display: flex;
    justify-content: center;
    width: 100%;
    background: #f5f5f5;
}
.con-box {
    width: 1300px;
    background-color: white;
    padding: 30px 20px;
}
</style>